// Simple test script to verify DOBSSNIssuanceCleanup fixes
import me.socure.common.kyc.model.es.result._
import me.socure.common.kyc.model._

object TestDOBSSNCleanup {
  def main(args: Array[String]): Unit = {
    println("Testing DOBSSNIssuanceCleanup fixes...")
    
    // Test case 1: Array index mismatch
    val records = Records(
      dob = Array("19750101", "19850101", "19950101"), // 3 DOBs
      ssn = Array("123456789"),
      ssnYearLow = Array("1980"),
      ssnYearHigh = Array("2000"),
      piiRowIDs = PiiRowIDs(dob = Array("PII1", "PII2")) // Only 2 piiRowIDs - mismatch!
    )
    
    val request = KycEntitySearchRequest(
      firstName = FirstName("John"),
      surName = SurName("Doe")
    )
    
    try {
      val result = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(request, records, None))
      println("✓ Array index mismatch handled gracefully")
      println(s"  Original DOBs: ${records.dob.length}, Result DOBs: ${result.records.dob.length}")
      println(s"  Original PII IDs: ${records.piiRowIDs.dob.length}, Result PII IDs: ${result.records.piiRowIDs.dob.length}")
    } catch {
      case e: Exception =>
        println(s"✗ Array index mismatch test failed: ${e.getMessage}")
    }
    
    // Test case 2: Input DOB preservation
    val recordsWithInputDOB = Records(
      dob = Array("19750101", "19850101", "19950101"), // 1975 (input), 1985, 1995
      ssn = Array("123456789"),
      ssnYearLow = Array("1980"),
      ssnYearHigh = Array("2000"),
      piiRowIDs = PiiRowIDs(dob = Array("PII1", "PII2", "PII3"))
    )
    
    val requestWithDOB = request.copy(dob = Some(DOB("19750101")))
    
    try {
      val result = DOBSSNIssuanceCleanup(RecordCleanupOperationInput(requestWithDOB, recordsWithInputDOB, None))
      println("✓ Input DOB preservation test completed")
      println(s"  Input DOB preserved: ${result.records.dob.contains("19750101")}")
    } catch {
      case e: Exception =>
        println(s"✗ Input DOB preservation test failed: ${e.getMessage}")
    }
    
    println("DOBSSNIssuanceCleanup testing completed!")
  }
}
