package me.socure.kycsearch.rulecodes.common

import me.socure.common.kyc.model.PiiAttribute._
import me.socure.common.kyc.model.es.result.Elastic4sResult
import me.socure.common.kyc.model.es.result.SSNWeightComputer.isRandomlyIssuedNationalIdHelper
import me.socure.common.kyc.model.{AddressComponents, AnchorDeceasedRecord, DOB, FirstName, KycEntitySearchRequest, NationalId, StreetAddress, SurName}
import me.socure.common.kyc.util.SSNUtil
import me.socure.kycsearch.matcher.AddressMatchers._
import me.socure.kycsearch.matcher.DOBMatchers._
import me.socure.kycsearch.matcher.NameMatchers.{SynonymsNameNickname, cleanN, matchN, matchNL}
import me.socure.kycsearch.matcher.NationalIDMacthers._
import me.socure.kycsearch.matcher.dEdit
import me.socure.kycsearch.model.{BestK<PERSON><PERSON>atch, Elastic4sFilteredNationalIdResults}
import org.slf4j.{Logger, LoggerFactory}
import java.io.{BufferedInputStream, InputStreamReader}
import org.apache.commons.csv.{CSVFormat, CSVParser}
import scala.collection.JavaConverters._
import scala.util.{Failure, Success, Try}

object NationalIDResolverHelper {
  val logger: Logger = LoggerFactory.getLogger(getClass)
  val InvalidStartNumber = "9"

  def isDeceasedIdentityAssociatedWithInputIdentity(piiMatchResults: Map[PiiAttribute, Int], flipped: Boolean): Boolean = {
    val firstname: Boolean = piiMatchResults(FirstNameMatch) == 1
    val surname: Boolean = piiMatchResults(SurNameMatch) == 1
    val dob: Boolean = piiMatchResults(DOBMatch) == 1
    val ssn: Boolean = piiMatchResults(SSNMatch) == 1
    val mobile: Boolean = piiMatchResults(MobileNumberMatch) == 1
    val street: Boolean = piiMatchResults(StreetAddressMatch) == 1
    val city: Boolean = piiMatchResults(CityMatch) == 1
    val state: Boolean = piiMatchResults(StateMatch) == 1
    val zip: Boolean = piiMatchResults(ZipCodeMatch) == 1
    val address:Boolean = (street && zip) || (street && city && state)
    if(ssn && (firstname || flipped)) {
      dob || surname || flipped || address || mobile
    } else if(dob && (firstname || flipped)) {
      mobile || address
    } else {
      false
    }
  }

  def isDeceased(isNationalIdValid: Boolean, originalId: NationalId, inputId: NationalId, cleanedNationalId: NationalId, foundResults: Elastic4sResult, anchorDeceasedRecordOpt: Option[AnchorDeceasedRecord]): Boolean = {
    if (cleanedNationalId.value.isEmpty) false
    else if (!isNationalIdValid) false
    else if (anchorDeceasedRecordOpt.isDefined) true
    else {
      val nonEmptyNationIDQueryResults = foundResults.attributes.filter(_.ssn.nonEmpty)
      if(nonEmptyNationIDQueryResults.nonEmpty) {
        val idIsDeceased = nonEmptyNationIDQueryResults.filter(cluster =>
          cluster.ssn.zip(cluster.ssnDeceased).exists {
            case (ssn: String, ssnDeceased: String) =>
              matchNationalId(inputId, NationalId(ssn), originalId = originalId) &&
                ssnDeceased.contains("1")
          })
        if (idIsDeceased.nonEmpty && cleanedNationalId.value.slice(0, 5) != "00000") true
        else false
      } else false
    }
  }

  def isIdentityDeceased(bestMatchedCidDeceased: Array[String], bestMatchedSsnDeceased: Array[String], isInputNationalIdPresent: Boolean): Boolean = {
    bestMatchedCidDeceased.contains("1") || (isInputNationalIdPresent && bestMatchedSsnDeceased.contains("1"))
  }

  def hasMatch(bestMatchEntity: BestKYCMatch, anchorRecord: AnchorDeceasedRecord): Boolean = {
    def getScore(aliases: Array[String], input: Option[String], scoreIfTrue: Double): Double = if (aliases.exists(alias => input.exists(_.equalsIgnoreCase(alias)))) scoreIfTrue else 0.0
    def getScoreDOB(aliases: Array[String], input: Option[String], scoreIfTrue: Double): Double = if (aliases.exists(alias => input.exists(inputDob => matchBDFuzzy(DOB(alias), DOB(inputDob))))) scoreIfTrue else 0.0
//    def getAddressScore(bmeRecord: BestKYCMatch, anchorRecord: AnchorDeceasedRecord, scoreIfTrue: Double) : Double = {
//      val streetAddressMatch = bmeRecord.cluster.streetAddress.exists(alias => anchorRecord.streetAddress.exists(_.equalsIgnoreCase(alias)))
//      val cityMatch = bmeRecord.cluster.city.exists(alias => anchorRecord.city.exists(_.equalsIgnoreCase(alias)))
//      val stateMatch = bmeRecord.cluster.state.exists(alias => anchorRecord.state.exists(_.equalsIgnoreCase(alias)))
//      val zipMatch = bmeRecord.cluster.zipCode.exists(alias => anchorRecord.zipCode.exists(_.equalsIgnoreCase(alias)))
//      if((streetAddressMatch && zipMatch) || (streetAddressMatch && cityMatch && stateMatch)) scoreIfTrue else 0.0
//    }
    val score = {
      getScore(bestMatchEntity.cluster.ssn, Some(anchorRecord.nationalId), 1.0) +
        getScore(bestMatchEntity.cluster.firstName, anchorRecord.firstName, 0.5) +
        getScore(bestMatchEntity.cluster.surName, anchorRecord.lastName, 0.5) +
        getScoreDOB(bestMatchEntity.cluster.dob, anchorRecord.dob, 1.0)
        //getAddressScore(bestMatchEntity,anchorRecord, 1.0)
    }
    score >= 2.5
  }

  def compareAnchorRecordWithInput(searchRequest: KycEntitySearchRequest, anchorRecord: AnchorDeceasedRecord): Boolean = {
    def getScore(record: Option[String], input: Option[String], scoreIfTrue: Double): Double = {
      val hasMatch = record.flatMap(r => input.map(_.equalsIgnoreCase(r)))
      if (hasMatch.getOrElse(false)) scoreIfTrue else 0.0
    }

    def getScoreDOB(record: Option[String], input: Option[String], scoreIfTrue: Double): Double = {
      val hasMatch = record.flatMap(r => input.map(inputDob => matchBDFuzzy(DOB(inputDob), DOB(r))))
      if (hasMatch.getOrElse(false)) scoreIfTrue else 0.0
    }

    val ssnScore = 1.0 // anchorRecord is fetched using input SSN, so score is 1 by default
    val nameScore = getScore(anchorRecord.firstName, Some(searchRequest.firstName.value), 0.5) +
      getScore(anchorRecord.lastName, Some(searchRequest.surName.value), 0.5)
    val dobScore = getScoreDOB(anchorRecord.dob, searchRequest.dob.map(_.value), 1.0)

    (ssnScore + nameScore + dobScore) >= 2.5
  }

  def getBMEScore(bestMatchEntity: BestKYCMatch, anchorRecord: AnchorDeceasedRecord): Double = {
    def getScore(aliases: Array[String], input: Option[String], scoreIfTrue: Double): Double = if (aliases.exists(alias => input.exists(_.equalsIgnoreCase(alias)))) scoreIfTrue else 0.0
    def getScoreDOB(aliases: Array[String], input: Option[String], scoreIfTrue: Double): Double = if (aliases.exists(alias => input.exists(inputDob => matchBDFuzzy(DOB(alias), DOB(inputDob))))) scoreIfTrue else 0.0
//    def getAddressScore(bmeRecord: BestKYCMatch, anchorRecord: AnchorDeceasedRecord, scoreIfTrue: Double) : Double = {
//      val streetAddressMatch = bmeRecord.cluster.streetAddress.exists(alias => anchorRecord.streetAddress.exists(_.equalsIgnoreCase(alias)))
//      val cityMatch = bmeRecord.cluster.city.exists(alias => anchorRecord.city.exists(_.equalsIgnoreCase(alias)))
//      val stateMatch = bmeRecord.cluster.state.exists(alias => anchorRecord.state.exists(_.equalsIgnoreCase(alias)))
//      val zipMatch = bmeRecord.cluster.zipCode.exists(alias => anchorRecord.zipCode.exists(_.equalsIgnoreCase(alias)))
//      if((streetAddressMatch && zipMatch) || (streetAddressMatch && cityMatch && stateMatch)) scoreIfTrue else 0.0
//    }
    val score = {
      getScore(bestMatchEntity.cluster.ssn, Some(anchorRecord.nationalId), 1.0) +
        getScore(bestMatchEntity.cluster.firstName, anchorRecord.firstName, 0.5) +
        getScore(bestMatchEntity.cluster.surName, anchorRecord.lastName, 0.5) +
        getScoreDOB(bestMatchEntity.cluster.dob, anchorRecord.dob, 1.0)
        //getAddressScore(bestMatchEntity,anchorRecord, 1.0)
    }
    score
  }

  def isIdInvalid(isNationalIdValid: Boolean, originalId: NationalId, inputId: NationalId, cleanedNationalId: NationalId, bestKYCMatch: BestKYCMatch, foundIdentities: Elastic4sResult): Boolean = {
    if (cleanedNationalId.value.isEmpty) false
    else if (!isNationalIdValid) true
    else if (cleanedNationalId.value.slice(0, 1) == InvalidStartNumber) true
    else if (cleanedNationalId.value.slice(0, 5) == "00000") {
      if (bestKYCMatch.cluster.ssn.isEmpty) false
      else if (bestKYCMatch.cluster.ssn.zip(bestKYCMatch.cluster.invalidSSN).filter{case (ssn:String,invalidSSN:String) => ssn.slice(0, 5) != "00000"}.exists{case (ssn:String,invalidSSN:String) => matchNationalId(inputId, NationalId(ssn), originalId = originalId) && (invalidSSN.slice(0, 1) == InvalidStartNumber || invalidSSN == "1")}) true
      else false
    }
    else if (foundIdentities.attributes.isEmpty || foundIdentities.attributes.exists(_.ssn.isEmpty)) false
    else if (foundIdentities.attributes.exists(cluster => cluster.ssn.zip(cluster.invalidSSN).exists{case (ssn:String,invalidSSN:String) => matchNationalId(inputId, NationalId(ssn), originalId = originalId) && invalidSSN == "1"})) true
    else false
  }

  def notInPublicRecord(isNationalIdValid: Boolean, originalId: NationalId, inputId: NationalId, cleanedNationalId: NationalId, foundResults: Elastic4sResult): Boolean = {
    if (cleanedNationalId.value.isEmpty) false
    else if (!isNationalIdValid) false
    else if (foundResults.attributes.exists(cluster => {
      cluster.ssn.exists(id => {
        matchNationalId(inputId, NationalId(id), originalId = originalId)
      })
    })
    ) false else true
  }

  def isNationalIdMissing(nationalId: NationalId): Boolean = {
    nationalId.value.isEmpty || nationalId.value == null
  }

  def isNotPrimaryId(isNationalIdValid: Boolean, originalId: NationalId, inputId: NationalId, cleanedNationalId: NationalId, matchedIds: Array[NationalId], bestMatch: BestKYCMatch): Boolean = {
    if (cleanedNationalId.value.isEmpty) false
    else if (!isNationalIdValid || cleanedNationalId.value.slice(0,5) == "00000") false
    else if (matchedIds.length <= 1) false
    else if (bestMatch.piiMatchResults.get(SSNMatch).contains(0)) false
    else if (!matchNationalIdForNonPrimarySSN(inputId, matchedIds.head, originalId = originalId)) true
    else false
  }

  def isIdITIN(cleanedNationalId: NationalId): Boolean = {
    if (cleanedNationalId.value.isEmpty) false
    else {
      val nGroup = cleanedNationalId.value.slice(3, 5).toInt
      if (cleanedNationalId.value.slice(0, 1) == InvalidStartNumber) {
        isITIN(nGroup)
      }
      else false
    }
  }

  def isBMEIdITIN(inputNationalId: String, bmeNationalId: String): Boolean = {
    if (bmeNationalId.isEmpty || inputNationalId.isEmpty) false
    else {
      val numericSSN = bmeNationalId.replaceAll("\\D", "")
      if (numericSSN == bmeNationalId && numericSSN.length == 9 && bmeNationalId.slice(0, 1) == InvalidStartNumber) {
        val nGroup = numericSSN.slice(3, 5).toInt
        isITIN(nGroup)
      }
      else false
    }
  }

  def isLastFourBMEIdITIN(inputNationalId: String, bmeNationalId: String): Boolean = {
    if (bmeNationalId.isEmpty || inputNationalId.isEmpty) false
    else {
      val numericSSN = bmeNationalId.replaceAll("\\D", "")
      if (numericSSN == bmeNationalId && numericSSN.length == 9 && inputNationalId.take(5) == "00000" && numericSSN.takeRight(4) == inputNationalId.takeRight(4) && numericSSN.slice(0, 1) == InvalidStartNumber) {
        val nGroup = numericSSN.slice(3, 5).toInt
        isITIN(nGroup)
      }
      else false
    }
  }

  private def isITIN(nGroup: Int) = {
    if (nGroup >= 50 && nGroup <= 65) true
    else if (nGroup >= 70 && nGroup <= 88) true
    else if (nGroup >= 90 && nGroup <= 92) true
    else if (nGroup >= 94 && nGroup <= 99) true
    else false
  }

  // TODO: Duplicated this method. Need to combine boolean and count based methods
  def idHasMultipleIdentities(isNationalIdValid: Boolean, originalId: NationalId, cleanedNationalId: NationalId, clustersIdentities: Elastic4sFilteredNationalIdResults, clustersC: Elastic4sResult): Boolean = {
    if (cleanedNationalId.value.isEmpty) false
    else if (!isNationalIdValid || cleanedNationalId.value.slice(0, 5) == "00000") false
    else {
      val matches = if (cleanedNationalId.value.slice(0, 5) != "00000") {
        clustersC
          .attributes
          .filter(cluster => cluster.ssn.zip(cluster.ciRowId).exists{case (ssn:String,ciRowId:String) => matchNationalId(cleanedNationalId, NationalId(ssn), originalId = originalId) && ciRowId.contains("CHM")})
      }
      else {
        clustersIdentities
          .attributes
          .filter(cluster => cluster.ssn.zip(cluster.ciRowId).exists{case (ssn:String,ciRowId:String) => matchNationalId(cleanedNationalId, NationalId(ssn), originalId = originalId) && ciRowId.contains("CHM")})
      }
      val NLs = matches.map(_.surName).distinct
      val BDs = matches.map(_.dob).distinct

      if (NLs.length <= 1) false
      else if (NLs.exists(nls => !NLs.head.exists(nlHead => nls.exists(nl => matchN(nl, nlHead))))) {
        if (BDs.exists(bds => !BDs.head.exists(bdHead => bds.exists(bd => matchBD(DOB(bd), DOB(bdHead)))))) true else false
      } else false
    }
  }

  def countIdHasMultipleIdentities(isNationalIdValid: Boolean, originalId: NationalId, cleanedNationalId: NationalId, clustersIdentities: Elastic4sFilteredNationalIdResults, clustersC: Elastic4sResult): Int = {
    if (cleanedNationalId.value.isEmpty) 0
    else if (!isNationalIdValid || cleanedNationalId.value.slice(0, 5) == "00000") 0
    else {
      val matches = if (cleanedNationalId.value.slice(0, 5) != "00000") {
        clustersC
          .attributes
          .filter(cluster => cluster.ssn.zip(cluster.ciRowId).exists{case (ssn:String,ciRowId:String) => matchNationalId(cleanedNationalId, NationalId(ssn), originalId = originalId) && ciRowId.contains("CHM")})
      }
      else {
        clustersIdentities
          .attributes
          .filter(cluster => cluster.ssn.zip(cluster.ciRowId).exists{case (ssn:String,ciRowId:String) => matchNationalId(cleanedNationalId, NationalId(ssn), originalId = originalId) && ciRowId.contains("CHM")})
      }
      val NLs = matches.map(_.surName).distinct
      val BDs = matches.map(_.dob).distinct

      if (NLs.length <= 1) 1
      else if (NLs.exists(nls => !NLs.head.exists(nlHead => nls.exists(nl => matchN(nl, nlHead))))) {
        BDs.count(bds => !BDs.head.exists(bdHead => bds.exists(bd => matchBD(DOB(bd), DOB(bdHead)))))
      } else 0
    }
  }

  def applicantHasMultipleIds(isNationalIdValid: Boolean, originalNationalId: NationalId, cleanedNationalId: NationalId, bestKYCMatch: BestKYCMatch): Boolean = {
    if (cleanedNationalId.value.isEmpty) false
    else if (!isNationalIdValid || cleanedNationalId.value.slice(0, 5) == "00000") false
    else if (bestKYCMatch.piiMatchResults.get(SSNMatch).contains(0)) false
    else {
      isBestMatchHaveMultipleSSN(bestKYCMatch, originalNationalId)
    }
  }

  private def isBestMatchHaveMultipleSSN(bestKYCMatch: BestKYCMatch, originalNationalId: NationalId) =  {
    val bestMatchNationalIds = bestKYCMatch.cluster.ssn.zip(bestKYCMatch.cluster.ciRowId)
            .map{case (nationalId:String,ciRowId:String) => (cleanNationalId(NationalId(nationalId)), NationalId(ciRowId))}
            .filter{case (nationalId1:NationalId,nationalId2:NationalId) => validateNationalId(originalId = originalNationalId, cleanedId = nationalId1) && (nationalId2.value.slice(0, 5) != "00000")}
            .filter{case (nationalId1:NationalId,nationalId2:NationalId) => !isIdITIN(nationalId1)}
            .distinct
          if (bestMatchNationalIds.length > 1) {
            val otherNationalIds = bestMatchNationalIds.tail.filter{case (nationalId1:NationalId,nationalId2:NationalId) => !matchNationalIdFuzzy(bestMatchNationalIds.head._1, nationalId1, originalId = bestMatchNationalIds.head._1) && !nationalId2.value.contains("LFM")}
            if (otherNationalIds.nonEmpty) true else
    false
          }
          else false
  }

// TODO: Duplicated this method. Need to combine boolean and count based methods
  def idNotAssociatedNameAddress(isNationalIdValid: Boolean, cleanedNationalId: NationalId, inputFirstName: FirstName, inputLastName: SurName,
                                 streetAddressOpt: Option[StreetAddress], clustersC: Elastic4sResult, addressMatchLogic: Option[String], addressComponents: Option[AddressComponents]): Boolean = {
    val cleanedFirstName = FirstName(cleanN(inputFirstName.value))
    val cleanLastName = SurName(cleanN(inputLastName.value))
    val streetAddressCleanedOpt = cleanStreetAddress(streetAddressOpt)

    if (cleanedNationalId.value.isEmpty || cleanedFirstName.value.isEmpty || cleanLastName.value.isEmpty || streetAddressCleanedOpt.forall(_.value.isEmpty)) false
    else if (!isNationalIdValid || cleanedNationalId.value.slice(0, 5) == "00000") false
    else if (clustersC.attributes.isEmpty) false
    else if (clustersC.attributes.head.ssn.isEmpty) false
    else {
      val matches = clustersC
        .attributes
        .filter(clusterC => clusterC.ssn.zip(clusterC.ciRowId).exists{case (ssn:String,ciRowID:String) => NationalId(ssn) == cleanedNationalId && ciRowID.contains("CHM")})         // v8
        .filter(_.firstName.nonEmpty)
        .filter(_.surName.nonEmpty)
      matches.exists(p = clusterC => (!clusterC.firstName.exists(matchN(cleanedFirstName.value, _)))
        && (!clusterC.surName.exists(x => matchNL(cleanLastName, SurName(x))))
        && (!clusterC.streetAddress.exists(addr => matchStreetAddressOptimized(streetAddressCleanedOpt, Some(StreetAddress(addr)), addressMatchLogic, addressComponents))))
    }
  }

  def countIdNotAssociatedNameAddress(isNationalIdValid: Boolean, cleanedNationalId: NationalId, inputFirstName: FirstName, inputLastName: SurName,
                                      streetAddressOpt: Option[StreetAddress], clustersC: Elastic4sResult, addressMatchLogic: Option[String], addressComponents: Option[AddressComponents]): Int = {
    val cleanedFirstName = FirstName(cleanN(inputFirstName.value))
    val cleanLastName = SurName(cleanN(inputLastName.value))
    val streetAddressCleanedOpt = cleanStreetAddress(streetAddressOpt)

    if (cleanedNationalId.value.isEmpty || cleanedFirstName.value.isEmpty || cleanLastName.value.isEmpty || streetAddressCleanedOpt.forall(_.value.isEmpty)) 0
    else if (!isNationalIdValid || cleanedNationalId.value.slice(0, 5) == "00000") 0
    else if (clustersC.attributes.isEmpty) 0
    else if (clustersC.attributes.head.ssn.isEmpty) 0
    else {
      val matches = clustersC
        .attributes
        .filter(clusterC => clusterC.ssn.zip(clusterC.ciRowId).exists{case (ssn:String,ciRowID:String) => NationalId(ssn) == cleanedNationalId && ciRowID.contains("CHM")})         // v8
        .filter(_.firstName.nonEmpty)
        .filter(_.surName.nonEmpty)
      matches.count(p = clusterC => (!clusterC.firstName.exists(matchN(cleanedFirstName.value, _)))
        && (!clusterC.surName.exists(x => matchNL(cleanLastName, SurName(x))))
        && (!clusterC.streetAddress.exists(addr => matchStreetAddressOptimized(streetAddressCleanedOpt, Some(StreetAddress(addr)), addressMatchLogic, addressComponents))))
    }
  }

  def idAssociatedDifferentName(isNationalIdValid: Boolean, cleanedNationalId: NationalId, inputFirstName: FirstName, inputSurName: SurName, clustersC: Elastic4sResult, bestKYCMatch : BestKYCMatch): Boolean = {
    val cleanedFirstName = cleanN(inputFirstName.value)
    val cleanedSurName = SurName(cleanN(inputSurName.value))
    if (cleanedNationalId.value.isEmpty || cleanedFirstName.isEmpty || cleanedSurName.value.isEmpty) false
    else if (!isNationalIdValid || cleanedNationalId.value.slice(0, 5) == "00000") false
    else if(bestKYCMatch.piiMatchResults.get(FirstNameMatch).contains(1) && bestKYCMatch.piiMatchResults.get(SurNameMatch).contains(0) && bestKYCMatch.piiMatchResults.get(SSNMatch).contains(1)){
      clustersC.attributes.exists(identity => {
        val foundFirstNameMatches = identity.firstName.count(matchN(cleanedFirstName, _))
        val foundLastNameMatches = identity.surName.count(x => matchNL(cleanedSurName, SurName(x)))
        (foundFirstNameMatches >= 1) && !(foundLastNameMatches >= 1)
      })
    }
    else {
      false
    }
  }

  def idAssociatedDifferentSurName(isNationalIdValid: Boolean, cleanedNationalId: NationalId, inputSurName: SurName, foundIdResults: Elastic4sResult): Boolean = {
    val nationalIDResults = foundIdResults.attributes.filter(_.ssn.nonEmpty)
    val cleanedSurName = SurName(cleanN(inputSurName.value))
    if (cleanedNationalId.value.isEmpty || cleanedSurName.value.isEmpty || nationalIDResults.isEmpty) false
    else if (!isNationalIdValid) false
    else {
      val foundSurNameMatch = nationalIDResults.exists(clusterC => clusterC.surName.exists(x => matchNL(cleanedSurName, SurName(x))))
      if(!foundSurNameMatch) true else false
    }
  }

  def isNonUsNationalId(isNationalIdValid: Boolean, originalId: NationalId, inputId: NationalId, cleanedNationalId: NationalId, nationalIdQueryResults: Elastic4sResult): Boolean = {
    if (cleanedNationalId.value.isEmpty) false
    else if (!isNationalIdValid || cleanedNationalId.value.slice(0, 5) == "00000") false        // CI invalid or last-four: don't fire
    else{
      val CIseries = cleanedNationalId.value.slice(0, 3).toInt
      if (CIseries >= 729 && CIseries <= 733) {
        val CIyearsHigh = nationalIdQueryResults.attributes.flatMap(clusterC => {
          val ps = clusterC.ssn zip clusterC.ssnYearHigh
          ps.filter{case (ssn:String,maxYear:String) => matchNationalId(inputId, NationalId(ssn), originalId = originalId)}.map{case (ssn:String,maxYear:String)  => maxYear}.filter(_.nonEmpty)
        })
        if (CIyearsHigh.exists(_.toInt <= 2011)) true else false
      }
      else false
    }
  }

  def isRandomlyIssuedNationalId(originalId: NationalId, inputId: NationalId, cleanedNationalId: NationalId, nationalIdQueryResults: Elastic4sResult): Boolean = {
    if (cleanedNationalId.value.isEmpty) false
    else {
      val CIyearsLow = nationalIdQueryResults.attributes.flatMap(clusterC => {
        val ps = clusterC.ssn zip clusterC.ssnYearLow
        ps.filter{case (ssn:String,minYear:String) => matchNationalId(inputId, NationalId(ssn), originalId = originalId)}.map{case (ssn:String,minYear:String) => minYear}.filter(_.nonEmpty)
      })
      if (CIyearsLow.exists(_.toInt >= 2011)) true
      else isNationalIdRandomlyAssigned(cleanedNationalId)
    }
  }

  def isNationalIdRandomlyAssigned(resolvedNationalId: NationalId): Boolean = {
    val ssnYearLow = SSNUtil.ssnLookup(Some(resolvedNationalId.value), useV2 = true)
                      .map(_.ssnYearLow)
                      .orElse(SSNUtil.ssnLookup(Some(resolvedNationalId.value))
                        .map(_.ssnYearLow))
                        .getOrElse("0")
    if (ssnYearLow.toInt >= 2011) true
    else isRandomlyIssuedNationalIdHelper(resolvedNationalId)
  }

  def nonRandomizedSSNOnFile(inputSSN:NationalId, bestOfTwoSources:BestKYCMatch): Boolean = {
    try{
      bestOfTwoSources.cluster.ssn.exists(nationalId =>
        isNineDigitsSSN(nationalId) && !inputSSN.onlyDigits.equals(nationalId) && !isNationalIdRandomlyAssigned(NationalId(nationalId))
      )
    }
    catch {
      case e: Throwable => false
    }
  }


  def doesNameAddressNationalIdMatch(bestMatch: BestKYCMatch): Boolean = {
    bestMatch.piiMatchResults.get(FirstNameMatch).contains(1) &&
      bestMatch.piiMatchResults.get(SurNameMatch).contains(1) &&
      bestMatch.piiMatchResults.get(SSNMatch).contains(1) &&
      bestMatch.piiMatchResults.get(StreetAddressMatch).contains(1) &&
      bestMatch.piiMatchResults.get(CityMatch).contains(1) &&
      bestMatch.piiMatchResults.get(ZipCodeMatch).contains(1) &&
      bestMatch.piiMatchResults.get(StateMatch).contains(1)
  }

  // TODO: Duplicated this method. Need to combine boolean and count based methods
  def isNationalIdAssociatedMultipleNames(isNationalIdValid: Boolean, originalId: NationalId, cleanedNationalId: NationalId, foundIdResults: Elastic4sFilteredNationalIdResults, nationalIdQueryResults: Elastic4sResult): Boolean = {
    if (cleanedNationalId.value.isEmpty) false
    else if (!isNationalIdValid || cleanedNationalId.value.slice(0, 5) == "00000") false
    else {
      val matches = if (cleanedNationalId.value.slice(0, 5) != "00000") {
        nationalIdQueryResults.attributes
          .filter(cluster => cluster.ssn.exists(c => matchNationalId(cleanedNationalId, NationalId(c), originalId = originalId)))
      }
      else {
        foundIdResults.attributes.filter(cluster => cluster.ssn.zip(cluster.ciRowId).exists{case (ssn:String,ciRowId:String) => matchNationalId(cleanedNationalId, NationalId(ssn), originalId = originalId) && ciRowId.contains("CHM")})
      }
      val NLs = matches.map(_.surName).distinct
      if (NLs.length <= 1) false
      else if (NLs.exists(nls => !NLs.head.exists(nlHead => nls.exists(nl => matchN(nl, nlHead))))) true else false
    }
  }

  def countNationalIdAssociatedMultipleNames(isNationalIdValid: Boolean, originalId: NationalId, cleanedNationalId: NationalId, foundIdResults: Elastic4sFilteredNationalIdResults, nationalIdQueryResults: Elastic4sResult, isNewVersion:Boolean = true): Int = {
    if (cleanedNationalId.value.isEmpty) 0
    else if (!isNationalIdValid || cleanedNationalId.value.slice(0, 5) == "00000") 0
    else {
      val matches = if (cleanedNationalId.value.slice(0, 5) != "00000") {
        nationalIdQueryResults.attributes
          .filter(cluster => cluster.ssn.exists(c => matchNationalId(cleanedNationalId, NationalId(c), originalId = originalId)))
      }
      else {
        foundIdResults.attributes.filter(cluster => cluster.ssn.zip(cluster.ciRowId).exists { case (ssn: String, ciRowId: String) => matchNationalId(cleanedNationalId, NationalId(ssn), originalId = originalId) && ciRowId.contains("CHM") })
      }
      if (isNewVersion) {
        val NLs = matches.map(_.surName).flatten.map(cleanN(_)).distinct
        if (NLs.length <= 1) 0
        else {
          val headElement = NLs.head
          NLs.count(nl => !matchN(nl, headElement))
        }
      } else {
        val NLs = matches.map(_.surName).distinct
        if (NLs.length <= 1) 1
        else NLs.count(nls => !NLs.head.exists(nlHead => nls.exists(nl => matchN(nl, nlHead))))
      }
    }
  }


  def firstNameNotMatchNationalId(isNationalIdValid: Boolean, cleanedNationalId: NationalId, inputFirstName: FirstName, foundIdResults: Elastic4sResult): Boolean = {
    val nationalIDResults = foundIdResults.attributes.filter(_.ssn.nonEmpty)
    val cleanedFirstName = cleanN(inputFirstName.value)
    if (cleanedNationalId.value.isEmpty || cleanedFirstName.isEmpty || nationalIDResults.isEmpty) false
    else if (!isNationalIdValid || cleanedNationalId.value.slice(0, 5) == "00000") false
    else {
      if (!nationalIDResults.exists(clusterC => clusterC.firstName.exists(retrievedFirstName => matchN(cleanedFirstName, retrievedFirstName))
        || clusterC.firstName.exists(retrievedFirstName => SynonymsNameNickname.get(cleanedFirstName).exists(matchedNames => matchedNames.contains(retrievedFirstName))))) true else false
    }
  }

  def isNationalIdIssuedLast3years(originalId: NationalId, inputId: NationalId, cleanedNationalId: NationalId, nationalIdQueryResults: Elastic4sResult): Boolean = {
    if (cleanedNationalId.value.isEmpty) false
    else {
      val CIyearsLow = nationalIdQueryResults.attributes.flatMap(clusterC => {
        val ps = clusterC.ssn zip clusterC.ssnYearLow
        ps.filter{case (ssn:String,minYear:String) => matchNationalId(inputId, NationalId(ssn), originalId = originalId)}.map{case (ssn:String,minYear:String) => minYear}.filter(_.nonEmpty)
      })
      val currentYear = java.util.Calendar.getInstance.get(java.util.Calendar.YEAR)
      if (CIyearsLow.exists(currentYear - _.toInt <= 3)) true else false
    }
  }

  def isNationalIdPossiblyMiskeyed(originalId: NationalId, cleanedNationalId: NationalId, bestMatch: BestKYCMatch, nationalIdMatchLogic: Option[String]): Boolean = {
    val isCleanedNationalIdEmpty = cleanedNationalId.value.isEmpty
    val clusterNationalIds = bestMatch.cluster.ssn
    lazy val exactMatchExistsInRecord = clusterNationalIds.exists(x => matchNationalId(cleanedNationalId, NationalId(x), originalId = originalId))
    val isPreferenceExactMatch = nationalIdMatchLogic.exists(_.equals("exact"))

    if (isCleanedNationalIdEmpty || isPreferenceExactMatch || exactMatchExistsInRecord) false
    else if (nationalIdMatchLogic.exists(_.equals("fuzzy"))) {
      clusterNationalIds.exists(x => matchNationalIdFuzzy(cleanedNationalId, NationalId(x), originalId = originalId))
    } else if (nationalIdMatchLogic.exists(_.equals("partial"))) {
      clusterNationalIds.exists(x => matchNationalIdPartial(cleanedNationalId, NationalId(x), originalId = originalId))
    } else false
  }

  def getResolved9DigitSSN(cleanedInputNationalId: NationalId, bestOfTwoSources: BestKYCMatch) : NationalId = {
    val piiMatchResults = bestOfTwoSources.piiMatchResults
    if(cleanedInputNationalId.value.slice(0, 5) == "00000" && canResolve9DigitSSN(piiMatchResults)){
      val matchingNationalIDs = bestOfTwoSources.cluster.ssn.filter(ssn => (ssn.takeRight(4) == cleanedInputNationalId.value.takeRight(4)
        && ssn.length == 9 && ssn.slice(0,5) != "00000"))
      if(matchingNationalIDs.length >= 1) {
         NationalId(matchingNationalIDs.head)
      }
      else cleanedInputNationalId
    }
    else cleanedInputNationalId
  }

  def canResolve9DigitSSN(piiMatchResults: Map[PiiAttribute,Int]) : Boolean = {
    piiMatchResults.get(FirstNameMatch).contains(1) && piiMatchResults.get(DOBMatch).contains(1) && piiMatchResults.get(SSNMatch).contains(1)
  }

  def is9DigitSSNResolved(cleanedInputNationalId: NationalId, bestOfTwoSources: BestKYCMatch) : (Boolean, NationalId) = {
    val piiMatchResults = bestOfTwoSources.piiMatchResults
    if (cleanedInputNationalId.value.slice(0, 5) == "00000" && canResolve9DigitSSN(piiMatchResults)){
      val matchingNationalIDs = bestOfTwoSources.cluster.ssn.filter(ssn => (ssn.takeRight(4) == cleanedInputNationalId.value.takeRight(4)
        && ssn.length == 9 && ssn.slice(0,5) != "00000"))
      if(matchingNationalIDs.length >= 1) {
        (true, NationalId(matchingNationalIDs.head))
      }
      else (false, cleanedInputNationalId)
    }
    else (false, cleanedInputNationalId)
  }


  def getClosestMatchedSSN(resolvedRequest : KycEntitySearchRequest, bestMatchEntity : BestKYCMatch) : String = {
    val inputNationalID = resolvedRequest.nationalId.getOrElse(NationalId("")).value
    val bestMatchedNationalID = bestMatchEntity.cluster.ssn
    if(bestMatchedNationalID.isEmpty) {
      ""
    }else {
      if (inputNationalID.length == 9 && inputNationalID.slice(0, 5) != "00000") {
        val nineDigitSSN = bestMatchedNationalID.filter(_.length == 9)
        if(nineDigitSSN.nonEmpty) nineDigitSSN.reduceLeft((bestMatchSSNA, bestMatchSSNB) => getSSNWithMinEditDistance(inputNationalID, bestMatchSSNA, bestMatchSSNB))
        else bestMatchedNationalID.reduceLeft((bestMatchSSNA, bestMatchSSNB) => getSSNWithMinEditDistance(inputNationalID, bestMatchSSNA, bestMatchSSNB))
      }
      else {
        val closestMatchedSSN = bestMatchedNationalID.filter(ssn => inputNationalID.takeRight(4) == ssn.takeRight(4))
        if(closestMatchedSSN.nonEmpty) closestMatchedSSN.maxBy(_.length)
        else bestMatchedNationalID.head
      }
    }
  }

  def getSSNWithMinEditDistance(inputSSN : String, bestMatchedSSNA : String, bestMatchedSSNB : String): String = {
    if(dEdit(inputSSN,bestMatchedSSNA) <= dEdit(inputSSN,bestMatchedSSNB) ) bestMatchedSSNA else bestMatchedSSNB
  }

  def isNineDigitsSSN(value: String): Boolean = {
    value.nonEmpty && value.length == 9 && value.slice(0, 5) != "00000"
  }

  def isSSNPresentOnFile(bestMatchEntity: BestKYCMatch): Boolean = {
    bestMatchEntity.cluster.ssn.nonEmpty && bestMatchEntity.cluster.ssn.exists(_.nonEmpty)
  }

  def isNonITINSSNPresentOnFile(bestMatchEntity: BestKYCMatch): Boolean = {
    bestMatchEntity.cluster.ssn.nonEmpty && bestMatchEntity.cluster.ssn.exists(ssn => ssn.nonEmpty && !isIdITIN(cleanNationalId(NationalId(ssn))))
  }

  def ssnFoundWithNoInput(isInputNationalIdPresent: Boolean, ssnPresentOnFile: Boolean):Boolean = {
    !isInputNationalIdPresent && ssnPresentOnFile
  }

  lazy val stateNameToAbbreviation: Map[String, String] = loadStateAbbreviations()

  def loadStateAbbreviations(): Map[String, String] = {
    Try {
      val inputStream = new BufferedInputStream(getClass.getResourceAsStream("/state_abbreviations.csv"))
      val csvParser = new CSVParser(new InputStreamReader(inputStream), CSVFormat.DEFAULT.withFirstRecordAsHeader())

      csvParser.iterator().asScala.map(record => {
        (record.get("state_name").toLowerCase.trim, record.get("state_abbreviation").toLowerCase.trim)
      }).toMap
    } match {
      case Success(value) => value
      case Failure(exception) =>
        logger.error("Error occurred when loading state abbreviations file", exception)
        Map.empty
    }
  }

  def getStateAbbreviation(stateName: String): Option[String] = {
    if (stateName == null || stateName.trim.isEmpty) {
      None
    } else {
      val cleanedStateName = stateName.toLowerCase.trim
      stateNameToAbbreviation.get(cleanedStateName)
    }
  }

  def isSSNIssuedStateNotInAssociatedAddresses(nationalId: Option[NationalId], bestOfTwoSources: Option[BestKYCMatch]): Boolean = {
    (nationalId, bestOfTwoSources) match {
      case (Some(ssn), Some(match_)) if ssn.value.nonEmpty && ssn.value.length >= 5 =>
        // Don't consider randomized SSNs for this rule
        if (isNationalIdRandomlyAssigned(ssn)) {
          return false
        }

        val ssnIssuedState = SSNUtil.ssnLookup(Some(ssn.value), useV2 = true)
          .flatMap(_.state)
          .orElse(SSNUtil.ssnLookup(Some(ssn.value))
            .flatMap(_.state))

        ssnIssuedState match {
          case Some(issuedState) if issuedState.nonEmpty =>
            val identityStates = match_.cluster.state.toSet

            if (identityStates.isEmpty) {
              return false
            }

            val issuedStateAbbreviation = getStateAbbreviation(issuedState)

            if (issuedStateAbbreviation.isEmpty) {
              logger.warn(s"Could not find abbreviation for state: $issuedState")
            }

            !identityStates.exists(state => {
              val stateLC = state.toLowerCase
              stateLC == issuedState.toLowerCase || issuedStateAbbreviation.contains(stateLC)
            })

          case _ => false
        }
      case _ => false
    }
  }
}
